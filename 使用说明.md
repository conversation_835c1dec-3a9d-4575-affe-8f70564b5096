# 📊 ETF持仓分析工具 - 使用说明

## 🎯 功能介绍

这是一个**一键式ETF持仓分析工具**，支持：
- 🔍 **单个ETF查询**：输入ETF代码，获取前十大持仓
- 📊 **批量Excel处理**：处理包含多个ETF的Excel文件
- 🌍 **全球市场支持**：支持A股、港股、美股、欧股等

## 🚀 一键使用方法

### 方法1：交互式使用（推荐新手）
```bash
python3 etf_analyzer.py
```
然后按提示选择功能即可！

### 方法2：命令行使用

#### 查询单个ETF
```bash
python3 etf_analyzer.py 513330
```

#### 处理Excel文件（所有工作表）
```bash
python3 etf_analyzer.py input/核心ETF十大持仓.xlsx
```

#### 处理Excel文件（指定工作表）
```bash
python3 etf_analyzer.py input/核心ETF十大持仓.xlsx 宽基指数ETF 境外ETF
```

## 📁 文件说明

### 核心文件
- **`etf_analyzer.py`** - 主程序，一键使用
- **`input/`** - 放置要处理的Excel文件
- **`使用说明.md`** - 本说明文件

### 输出文件
- **`etf_代码_holdings_时间.txt`** - 单个ETF查询结果
- **`文件名_updated.xlsx`** - 更新后的Excel文件（包含持仓数据）
- **`文件名_summary_时间.csv`** - CSV汇总文件

## 📋 Excel文件格式要求

Excel文件中只需要包含ETF代码（6位数字），程序会自动识别：

| ETF代码 | ETF名称 | 其他列... |
|---------|---------|-----------|
| 510050  | 华夏上证50ETF | ... |
| 513330  | 华夏恒生互联网科技业ETF | ... |

## 🎯 使用示例

### 示例1：查询单个ETF
```bash
$ python3 etf_analyzer.py 513330

正在查询ETF 513330...

✅ 华夏恒生互联网科技业ETF 前十大持仓:
--------------------------------------------------------------------------------
 1. 01024    快手-W                     12.48%
 2. 09999    网易-S                     11.48%
 3. 00700    腾讯控股                   10.87%
 4. 09618    京东集团-SW                10.66%
 5. 09988    阿里巴巴-W                 10.28%
 6. 03690    美团-W                     9.83%
 7. 09888    百度集团-SW                6.88%
 8. 09626    哔哩哔哩-W                 3.40%
 9. 00268    金蝶国际                   3.19%
10. 00020    商汤-W                     2.73%
--------------------------------------------------------------------------------
✅ 数据已保存到: etf_513330_holdings_20250915_201234.txt
```

### 示例2：处理Excel文件
```bash
$ python3 etf_analyzer.py input/核心ETF十大持仓.xlsx

正在处理Excel文件: input/核心ETF十大持仓.xlsx
发现工作表: 宽基指数ETF, 细分行业ETF, 境外ETF, 债券ETF, 商品和货币ETF

📋 处理工作表: 宽基指数ETF
找到 20 个ETF代码
[1/20] 处理 510050...
  ✅ 成功
[2/20] 处理 159901...
  ✅ 成功
...

🎉 处理完成!
总计: 148 个ETF
成功: 125 个
失败: 23 个
成功率: 84.5%

✅ Excel文件已更新: input/核心ETF十大持仓_updated.xlsx
✅ CSV汇总文件已生成: input/核心ETF十大持仓_summary_20250915_201456.csv
```

## 🌍 支持的ETF类型

- **🇨🇳 A股ETF**：沪深300、中证500、创业板等
- **🇭🇰 港股ETF**：恒生指数、恒生科技等  
- **🇺🇸 美股ETF**：纳斯达克100、标普500等
- **🇪🇺 欧股ETF**：法国CAC40、德国DAX等
- **🏢 行业ETF**：科技、医药、消费等
- **💰 主题ETF**：新能源、人工智能等

## ❓ 常见问题

### Q: 为什么有些ETF获取失败？
A: 可能原因：
- 数据源暂时无法访问
- ETF代码错误
- 该ETF暂无持仓数据

### Q: 支持哪些文件格式？
A: 目前支持：
- Excel文件（.xlsx）
- 输出CSV文件（.csv）

### Q: 数据多久更新一次？
A: 数据来源于公开的基金网站，通常是季度更新

### Q: 可以同时处理多个Excel文件吗？
A: 目前一次只能处理一个Excel文件，但支持多个工作表

## 🔧 环境要求

需要安装以下Python包：
```bash
pip install requests beautifulsoup4 pandas openpyxl
```

## 📞 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. ETF代码是否正确（6位数字）
3. Excel文件格式是否正确

---

**🎉 现在就开始使用吧！只需要一行命令：`python3 etf_analyzer.py`**

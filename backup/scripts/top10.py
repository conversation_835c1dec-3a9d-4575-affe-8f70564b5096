#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用ETF最新季度持仓前十大股票获取脚本
支持任意ETF代码查询
"""

import requests
from datetime import datetime
import re
from bs4 import BeautifulSoup
import sys

class ETFCrawler:
    def __init__(self, etf_code):
        self.etf_code = etf_code
        self.etf_name = ""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def is_valid_stock_code(self, stock_code):
        """验证股票代码是否有效，支持多种格式"""
        if not stock_code or len(stock_code) < 2:
            return False

        # A股代码：6位数字（如000001, 600000, 300001）
        if len(stock_code) == 6 and stock_code.isdigit():
            return True

        # 港股代码：5位，以0开头（如01024）
        if len(stock_code) == 5 and stock_code.startswith('0') and stock_code[1:].isdigit():
            return True

        # 美股代码：1-5位字母（如AAPL, MSFT, GOOGL）
        if 1 <= len(stock_code) <= 5 and stock_code.isalpha():
            return True

        # 欧洲股票代码：通常是字母+数字组合（如SUFP, TTEFP, MCFP）
        if 2 <= len(stock_code) <= 10 and stock_code.isalnum():
            return True

        # 其他可能的格式：包含字母和数字的组合
        if 2 <= len(stock_code) <= 15 and re.match(r'^[A-Za-z0-9]+$', stock_code):
            return True

        return False

    def get_holdings_from_eastmoney(self):
        """从东方财富获取持仓数据"""
        try:
            # 尝试多个东方财富接口，按优先级排序
            urls = [
                f"https://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=jjcc&code={self.etf_code}&topline=10",
                f"http://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=jjcc&code={self.etf_code}&topline=10",
                f"http://fundf10.eastmoney.com/ccmx_{self.etf_code}.html",
                f"https://fund.eastmoney.com/{self.etf_code}.html"
            ]

            for url in urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=15)
                    response.encoding = 'utf-8'

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_eastmoney_html(soup)
                        if holdings:
                            return holdings
                except:
                    continue

        except Exception as e:
            print(f"从东方财富获取数据失败: {e}")

        return None

    def parse_eastmoney_html(self, soup):
        """解析东方财富HTML数据"""
        try:
            holdings = []

            # 查找持仓表格的多种可能结构
            table_selectors = [
                'table.w782.comm.tzxq',
                'table[class*="tzxq"]',
                'table[class*="comm"]',
                '.boxitem table',
                'table.comm.lsjz',
                'table'
            ]

            for selector in table_selectors:
                tables = soup.select(selector)
                for table in tables:
                    rows = table.find_all('tr')
                    if len(rows) > 1:
                        # 检查表头是否包含相关字段
                        header_row = rows[0]
                        header_cells = header_row.find_all(['th', 'td'])
                        header_text = ' '.join([cell.get_text().strip() for cell in header_cells])

                        if any(keyword in header_text for keyword in ['股票代码', '股票名称', '持仓比例', '市值比例', '占净值比例']):
                            # 找到表头中各列的位置
                            code_col = name_col = ratio_col = -1
                            for i, cell in enumerate(header_cells):
                                cell_text = cell.get_text().strip()
                                if '股票代码' in cell_text or '证券代码' in cell_text:
                                    code_col = i
                                elif '股票名称' in cell_text or '证券名称' in cell_text:
                                    name_col = i
                                elif '持仓比例' in cell_text or '市值比例' in cell_text or '占净值比例' in cell_text:
                                    ratio_col = i

                            # 解析数据行
                            for i, row in enumerate(rows[1:11]):  # 前10行
                                cells = row.find_all('td')
                                if len(cells) >= 3:  # 至少要有3列数据
                                    # 提取数据，处理列索引越界问题
                                    stock_code = ""
                                    stock_name = ""
                                    holding_ratio = ""

                                    if code_col >= 0 and code_col < len(cells):
                                        stock_code = cells[code_col].get_text().strip()
                                    if name_col >= 0 and name_col < len(cells):
                                        stock_name = cells[name_col].get_text().strip()
                                    if ratio_col >= 0 and ratio_col < len(cells):
                                        holding_ratio = cells[ratio_col].get_text().strip()

                                    # 如果没有找到明确的列位置，尝试按常见顺序解析
                                    if not stock_code and not stock_name:
                                        if len(cells) >= 3:
                                            # 常见格式：序号、股票代码、股票名称、占净值比例...
                                            stock_code = cells[1].get_text().strip() if len(cells) > 1 else ""
                                            stock_name = cells[2].get_text().strip() if len(cells) > 2 else ""
                                            # 查找比例列
                                            for j in range(3, min(len(cells), 8)):
                                                cell_text = cells[j].get_text().strip()
                                                if '%' in cell_text:
                                                    holding_ratio = cell_text
                                                    break

                                    # 清理股票代码（保留数字和字母，处理各种代码格式）
                                    if stock_code:
                                        # 清理特殊字符，保留字母和数字
                                        stock_code = re.sub(r'[^\d\w]', '', stock_code)

                                    # 清理持仓比例
                                    if holding_ratio:
                                        ratio_match = re.search(r'(\d+\.?\d*%)', holding_ratio)
                                        if ratio_match:
                                            holding_ratio = ratio_match.group(1)
                                        else:
                                            holding_ratio = ""

                                    # 验证数据有效性（支持多种股票代码格式）
                                    if stock_code and stock_name and self.is_valid_stock_code(stock_code):
                                        holdings.append({
                                            'rank': i + 1,
                                            'stock_code': stock_code,
                                            'stock_name': stock_name,
                                            'holding_ratio': holding_ratio if holding_ratio else "",
                                            'holding_shares': ''
                                        })

                            if holdings:
                                return holdings

            return None

        except Exception as e:
            print(f"解析东方财富HTML数据失败: {e}")
            return None



    def get_holdings_from_sina(self):
        """从新浪财经获取持仓数据"""
        try:
            url = f"https://finance.sina.com.cn/fund/quotes/{self.etf_code}/bc.shtml"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.encoding = 'utf-8'

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                return self.parse_sina_data(soup)

        except Exception as e:
            print(f"从新浪财经获取数据失败: {e}")

        return None

    def parse_sina_data(self, soup):
        """解析新浪财经数据"""
        try:
            holdings = []
            # 查找持仓表格
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')
                if len(rows) > 1:
                    # 检查是否是持仓表格
                    header = rows[0].find_all(['th', 'td'])
                    if any('股票代码' in cell.get_text() or '持仓比例' in cell.get_text() for cell in header):
                        for i, row in enumerate(rows[1:11]):  # 前10行
                            cells = row.find_all('td')
                            if len(cells) >= 3:
                                stock_code = cells[0].get_text().strip()
                                stock_name = cells[1].get_text().strip()
                                holding_ratio = cells[2].get_text().strip()

                                holdings.append({
                                    'rank': i + 1,
                                    'stock_code': stock_code,
                                    'stock_name': stock_name,
                                    'holding_ratio': holding_ratio,
                                    'holding_shares': 'N/A'
                                })
                        break

            return holdings if holdings else None

        except Exception as e:
            print(f"解析新浪财经数据失败: {e}")

        return None

    def get_holdings_from_tiantian(self):
        """从天天基金获取持仓数据"""
        try:
            # 使用正确的天天基金API接口
            import time
            timestamp = str(int(time.time() * 1000))
            urls = [
                f"https://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=jjcc&code={self.etf_code}&topline=10&rt={timestamp}",
                f"http://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=jjcc&code={self.etf_code}&topline=10",
                f"http://fundf10.eastmoney.com/ccmx_{self.etf_code}.html"
            ]

            for url in urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=10)
                    response.encoding = 'utf-8'

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_tiantian_data(soup)
                        if holdings:
                            return holdings
                except:
                    continue

        except Exception as e:
            print(f"从天天基金获取数据失败: {e}")

        return None

    def parse_tiantian_data(self, soup):
        """解析天天基金数据"""
        try:
            holdings = []

            # 查找持仓表格的多种可能结构
            table_selectors = [
                'table.w782.comm.tzxq',
                'table[class*="tzxq"]',
                'table[class*="comm"]',
                'table'
            ]

            for selector in table_selectors:
                tables = soup.select(selector)
                for table in tables:
                    rows = table.find_all('tr')
                    if len(rows) > 1:
                        # 检查表头是否包含相关字段
                        header_row = rows[0]
                        header_cells = header_row.find_all(['th', 'td'])
                        header_text = ' '.join([cell.get_text().strip() for cell in header_cells])

                        if any(keyword in header_text for keyword in ['股票代码', '股票名称', '持仓比例', '市值比例', '占净值比例']):
                            # 解析数据行
                            for i, row in enumerate(rows[1:11]):  # 前10行
                                cells = row.find_all('td')
                                if len(cells) >= 3:
                                    # 尝试不同的列位置
                                    stock_code = ""
                                    stock_name = ""
                                    holding_ratio = ""

                                    # 方法1：按表头找列位置
                                    code_col = name_col = ratio_col = -1
                                    for j, cell in enumerate(header_cells):
                                        cell_text = cell.get_text().strip()
                                        if '股票代码' in cell_text or '证券代码' in cell_text:
                                            code_col = j
                                        elif '股票名称' in cell_text or '证券名称' in cell_text:
                                            name_col = j
                                        elif '持仓比例' in cell_text or '市值比例' in cell_text or '占净值比例' in cell_text:
                                            ratio_col = j

                                    if code_col >= 0 and name_col >= 0 and len(cells) > max(code_col, name_col):
                                        stock_code = cells[code_col].get_text().strip()
                                        stock_name = cells[name_col].get_text().strip()
                                        if ratio_col >= 0 and ratio_col < len(cells):
                                            holding_ratio = cells[ratio_col].get_text().strip()
                                    else:
                                        # 方法2：按常见格式解析（序号、代码、名称、比例...）
                                        if len(cells) >= 4:
                                            stock_code = cells[1].get_text().strip()
                                            stock_name = cells[2].get_text().strip()
                                            # 查找包含%的列作为比例
                                            for j in range(3, min(len(cells), 8)):
                                                cell_text = cells[j].get_text().strip()
                                                if '%' in cell_text:
                                                    holding_ratio = cell_text
                                                    break

                                    # 清理数据
                                    if stock_code:
                                        stock_code = re.sub(r'[^\d\w]', '', stock_code)

                                    if holding_ratio:
                                        ratio_match = re.search(r'(\d+\.?\d*%)', holding_ratio)
                                        if ratio_match:
                                            holding_ratio = ratio_match.group(1)
                                        else:
                                            holding_ratio = ""

                                    # 验证数据有效性（支持多种股票代码格式）
                                    if stock_code and stock_name and self.is_valid_stock_code(stock_code):
                                        holdings.append({
                                            'rank': i + 1,
                                            'stock_code': stock_code,
                                            'stock_name': stock_name,
                                            'holding_ratio': holding_ratio,
                                            'holding_shares': ''
                                        })

                            if holdings:
                                return holdings

            return None

        except Exception as e:
            print(f"解析天天基金数据失败: {e}")
            return None

    def get_etf_name(self):
        """获取ETF名称"""
        try:
            url = f"https://fund.eastmoney.com/{self.etf_code}.html"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.encoding = 'utf-8'

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 查找ETF名称
                title_elements = soup.find_all(['title', 'h1', 'h2'])
                for element in title_elements:
                    text = element.get_text().strip()
                    if self.etf_code in text and ('ETF' in text or '基金' in text):
                        # 提取基金名称
                        name_match = re.search(r'([^(（]+(?:ETF|基金))', text)
                        if name_match:
                            self.etf_name = name_match.group(1).strip()
                            return self.etf_name
        except:
            pass

        return f"ETF({self.etf_code})"

    def get_top_holdings(self):
        """获取前十大持仓股票"""
        print(f"正在获取ETF（{self.etf_code}）最新季度持仓数据...")

        # 获取ETF名称
        self.etf_name = self.get_etf_name()
        print(f"ETF名称: {self.etf_name}")

        # 尝试多个数据源
        holdings = None

        # 首先尝试东方财富
        print("尝试从东方财富获取数据...")
        holdings = self.get_holdings_from_eastmoney()

        if not holdings:
            print("尝试从天天基金获取数据...")
            holdings = self.get_holdings_from_tiantian()

        if not holdings:
            print("尝试从新浪财经获取数据...")
            holdings = self.get_holdings_from_sina()

        if not holdings:
            print("❌ 所有数据源都无法获取到持仓数据")
            return None

        return holdings



    def save_to_file(self, holdings, filename=None):
        """将持仓数据保存到文件（表格格式）"""
        try:
            if not filename:
                filename = f"etf_{self.etf_code}_top10_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 准备持仓股票名称列表
            stock_names = []
            stock_ratios = []

            for holding in holdings:
                name = holding.get('stock_name', '').strip()
                ratio = holding.get('holding_ratio', '').strip()

                if name and name != 'N/A':
                    stock_names.append(name)
                if ratio and ratio != 'N/A':
                    stock_ratios.append(ratio)

            # 将股票名称和持仓比例组合
            holdings_text = ""
            for i, name in enumerate(stock_names):
                if i < len(stock_ratios):
                    holdings_text += f"{name}({stock_ratios[i]})"
                else:
                    holdings_text += name

                if i < len(stock_names) - 1:
                    holdings_text += "、"

            with open(filename, 'w', encoding='utf-8') as f:
                # 写入表格格式
                f.write("┌" + "─" * 12 + "┬" + "─" * 20 + "┬" + "─" * 80 + "┐\n")
                f.write("│" + "代码".center(10) + "│" + "名称".center(18) + "│" + "十大持仓".center(78) + "│\n")
                f.write("├" + "─" * 12 + "┼" + "─" * 20 + "┼" + "─" * 80 + "┤\n")

                # 分行显示持仓信息
                lines = []
                current_line = ""
                for char in holdings_text:
                    if len(current_line.encode('utf-8')) >= 75:  # 考虑中文字符宽度
                        lines.append(current_line)
                        current_line = char
                    else:
                        current_line += char
                if current_line:
                    lines.append(current_line)

                # 写入第一行数据
                if lines:
                    f.write("│" + f"{self.etf_code}".center(10) + "│" + f"{self.etf_name}".center(18) + "│" + f"{lines[0]}".ljust(78) + "│\n")

                    # 写入剩余行
                    for line in lines[1:]:
                        f.write("│" + " " * 12 + "│" + " " * 20 + "│" + f"{line}".ljust(78) + "│\n")
                else:
                    f.write("│" + f"{self.etf_code}".center(10) + "│" + f"{self.etf_name}".center(18) + "│" + "暂无数据".ljust(78) + "│\n")

                f.write("└" + "─" * 12 + "┴" + "─" * 20 + "┴" + "─" * 80 + "┘\n")
                f.write(f"\n获取时间: {current_time}\n")
                f.write("注：数据来源于公开信息，仅供参考\n")

            print(f"数据已成功保存到文件: {filename}")
            return True

        except Exception as e:
            print(f"保存文件失败: {e}")
            return False

    def display_holdings(self, holdings):
        """在控制台显示持仓数据"""
        print(f"\n{'='*80}")
        print(f"ETF（{self.etf_code}）- {self.etf_name} 最新季度持仓前十大股票")
        print(f"{'='*80}")
        print(f"{'排名':<4} {'股票代码':<10} {'股票名称':<20} {'持仓比例':<10}")
        print("-" * 80)

        for holding in holdings:
            rank = holding.get('rank', 'N/A')
            code = holding.get('stock_code', 'N/A')
            name = holding.get('stock_name', 'N/A')
            ratio = holding.get('holding_ratio', 'N/A')

            print(f"{rank:<4} {code:<10} {name:<20} {ratio:<10}")

        print("-" * 80)


def get_etf_code():
    """获取用户输入的ETF代码"""
    if len(sys.argv) > 1:
        return sys.argv[1]
    else:
        return input("请输入ETF代码（如562500）: ").strip()


def main():
    """主函数"""
    try:
        # 获取ETF代码
        etf_code = get_etf_code()

        if not etf_code:
            print("❌ ETF代码不能为空")
            return

        # 验证ETF代码格式
        if not re.match(r'^\d{6}$', etf_code):
            print("❌ ETF代码格式不正确，应为6位数字")
            return

        # 创建爬虫实例
        crawler = ETFCrawler(etf_code)

        # 获取持仓数据
        holdings = crawler.get_top_holdings()

        if holdings:
            # 显示数据
            crawler.display_holdings(holdings)

            # 保存到文件
            success = crawler.save_to_file(holdings)

            if success:
                print(f"\n✅ 成功获取并保存了ETF（{etf_code}）前十大持仓股票数据")
            else:
                print("\n❌ 数据获取成功但保存失败")
        else:
            print(f"\n❌ 无法获取ETF（{etf_code}）的持仓数据，请检查代码是否正确或稍后重试")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")


if __name__ == "__main__":
    main()
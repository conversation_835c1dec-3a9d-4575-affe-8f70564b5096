#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF持仓查询工具演示脚本
展示如何使用单个和批量查询功能
"""

import os
import sys
from datetime import datetime

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("ETF持仓查询工具演示")
    print("="*60)
    print("1. 单个ETF查询演示")
    print("2. 批量ETF查询演示")
    print("3. 查看现有结果文件")
    print("4. 清理临时文件")
    print("0. 退出")
    print("="*60)

def demo_single_etf():
    """演示单个ETF查询"""
    print("\n📊 单个ETF查询演示")
    print("-" * 40)
    
    # 示例ETF代码
    demo_codes = ['510050', '159915', '562500', '510300']
    
    print("可选的演示ETF代码:")
    for i, code in enumerate(demo_codes, 1):
        print(f"{i}. {code}")
    print("5. 自定义输入")
    
    try:
        choice = input("\n请选择要查询的ETF (1-5): ").strip()
        
        if choice in ['1', '2', '3', '4']:
            etf_code = demo_codes[int(choice) - 1]
        elif choice == '5':
            etf_code = input("请输入ETF代码: ").strip()
        else:
            print("❌ 无效选择")
            return
        
        print(f"\n正在查询ETF {etf_code}...")
        
        # 执行查询
        cmd = f"python3 top10.py {etf_code}"
        os.system(cmd)
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def demo_batch_etf():
    """演示批量ETF查询"""
    print("\n📊 批量ETF查询演示")
    print("-" * 40)
    
    ods_file = "CoreETFTopTenHoldings.ods"
    
    if not os.path.exists(ods_file):
        print(f"❌ 找不到ETF列表文件: {ods_file}")
        print("请确保该文件存在并包含ETF代码列表")
        return
    
    print(f"✅ 找到ETF列表文件: {ods_file}")
    
    confirm = input("是否开始批量查询？这可能需要几分钟时间 (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        print("\n正在执行批量查询...")
        print("⚠️  请耐心等待，这可能需要几分钟...")
        
        # 执行批量查询
        cmd = "python3 batch_etf_crawler.py"
        os.system(cmd)
        
        print("\n✅ 批量查询完成！")
        print("请查看生成的文件:")
        print("- all_etf_holdings_*.txt (文本报告)")
        print("- CoreETFTopTenHoldings_updated.xlsx (Excel文件)")
        print("- CoreETFTopTenHoldings_updated_new.ods (ODS文件)")
    else:
        print("❌ 已取消批量查询")

def show_result_files():
    """显示现有结果文件"""
    print("\n📁 现有结果文件")
    print("-" * 40)
    
    # 查找各种类型的结果文件
    files = []
    
    for filename in os.listdir('.'):
        if (filename.startswith('etf_') and filename.endswith('.txt')) or \
           (filename.startswith('all_etf_holdings_') and filename.endswith('.txt')) or \
           (filename.startswith('CoreETFTopTenHoldings_') and filename.endswith(('.xlsx', '.ods'))):
            files.append(filename)
    
    if files:
        files.sort(reverse=True)  # 按时间倒序
        print("找到以下结果文件:")
        for i, filename in enumerate(files, 1):
            file_size = os.path.getsize(filename)
            file_time = datetime.fromtimestamp(os.path.getmtime(filename))
            print(f"{i:2d}. {filename}")
            print(f"    大小: {file_size:,} 字节, 修改时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 提供查看选项
        try:
            choice = input(f"\n输入文件编号查看内容 (1-{len(files)})，或按回车返回: ").strip()
            if choice and choice.isdigit():
                file_index = int(choice) - 1
                if 0 <= file_index < len(files):
                    filename = files[file_index]
                    print(f"\n📄 文件内容预览: {filename}")
                    print("-" * 60)
                    
                    if filename.endswith('.txt'):
                        # 显示文本文件的前50行
                        with open(filename, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            for i, line in enumerate(lines[:50], 1):
                                print(f"{i:3d}: {line.rstrip()}")
                            if len(lines) > 50:
                                print(f"... (还有 {len(lines) - 50} 行)")
                    else:
                        print("这是一个Excel/ODS文件，请使用相应的软件打开查看")
        except ValueError:
            pass
    else:
        print("❌ 未找到任何结果文件")
        print("请先运行查询脚本生成数据")

def clean_temp_files():
    """清理临时文件"""
    print("\n🧹 清理临时文件")
    print("-" * 40)
    
    # 查找临时文件
    temp_patterns = ['__pycache__', '*.pyc', '*_temp.*', '*_backup_*']
    temp_files = []
    
    # 查找__pycache__目录
    if os.path.exists('__pycache__'):
        temp_files.append('__pycache__/')
    
    # 查找备份文件
    for filename in os.listdir('.'):
        if '_backup_' in filename or '_temp' in filename:
            temp_files.append(filename)
    
    if temp_files:
        print("找到以下临时文件:")
        for filename in temp_files:
            print(f"- {filename}")
        
        confirm = input("\n是否删除这些临时文件? (y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            import shutil
            for filename in temp_files:
                try:
                    if os.path.isdir(filename):
                        shutil.rmtree(filename)
                    else:
                        os.remove(filename)
                    print(f"✅ 已删除: {filename}")
                except Exception as e:
                    print(f"❌ 删除失败 {filename}: {e}")
        else:
            print("❌ 已取消清理")
    else:
        print("✅ 没有找到临时文件")

def main():
    """主函数"""
    try:
        while True:
            show_menu()
            choice = input("请选择操作 (0-4): ").strip()
            
            if choice == '0':
                print("\n👋 再见！")
                break
            elif choice == '1':
                demo_single_etf()
            elif choice == '2':
                demo_batch_etf()
            elif choice == '3':
                show_result_files()
            elif choice == '4':
                clean_temp_files()
            else:
                print("❌ 无效选择，请输入 0-4")
            
            input("\n按回车键继续...")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格文件格式转换工具
支持CSV、XLSX、ODS格式之间的相互转换
"""

import pandas as pd
import os
import sys
from datetime import datetime
import traceback

class FormatConverter:
    def __init__(self):
        self.supported_formats = {
            '.csv': {'engine': None, 'description': 'CSV文件'},
            '.xlsx': {'engine': 'openpyxl', 'description': 'Excel文件'},
            '.ods': {'engine': 'odf', 'description': 'OpenDocument表格文件'}
        }
    
    def read_file(self, file_path):
        """读取文件，自动识别格式"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_ext}")
            
            print(f"正在读取{self.supported_formats[file_ext]['description']}: {file_path}")
            
            if file_ext == '.csv':
                # 尝试不同编码读取CSV
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        print(f"成功使用{encoding}编码读取CSV文件")
                        return df
                    except UnicodeDecodeError:
                        continue
                raise Exception("无法使用常见编码读取CSV文件")
            
            elif file_ext == '.xlsx':
                return pd.read_excel(file_path, engine='openpyxl')
            
            elif file_ext == '.ods':
                return pd.read_excel(file_path, engine='odf')
                
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
    
    def write_file(self, df, file_path):
        """写入文件，根据扩展名选择格式"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_ext}")
            
            print(f"正在保存{self.supported_formats[file_ext]['description']}: {file_path}")
            
            if file_ext == '.csv':
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            elif file_ext == '.xlsx':
                df.to_excel(file_path, index=False, engine='openpyxl')
            elif file_ext == '.ods':
                df.to_excel(file_path, index=False, engine='odf')
            
            file_size = os.path.getsize(file_path)
            print(f"✅ 文件保存成功: {file_path} ({file_size:,} 字节)")
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False
    
    def convert_file(self, input_path, output_path):
        """转换文件格式"""
        try:
            # 读取输入文件
            df = self.read_file(input_path)
            if df is None:
                return False
            
            print(f"文件包含 {len(df)} 行 {len(df.columns)} 列数据")
            
            # 写入输出文件
            success = self.write_file(df, output_path)
            
            if success:
                print(f"✅ 格式转换成功: {input_path} -> {output_path}")
            
            return success
            
        except Exception as e:
            print(f"❌ 格式转换失败: {e}")
            return False
    
    def convert_to_all_formats(self, input_path):
        """将文件转换为所有支持的格式"""
        try:
            # 读取输入文件
            df = self.read_file(input_path)
            if df is None:
                return []
            
            print(f"文件包含 {len(df)} 行 {len(df.columns)} 列数据")
            
            # 生成基础文件名
            base_name = os.path.splitext(input_path)[0]
            input_ext = os.path.splitext(input_path)[1].lower()
            
            output_files = []
            
            # 转换为所有格式
            for ext, info in self.supported_formats.items():
                if ext != input_ext:  # 跳过输入文件的格式
                    output_path = base_name + '_converted' + ext
                    if self.write_file(df, output_path):
                        output_files.append(output_path)
            
            return output_files
            
        except Exception as e:
            print(f"❌ 批量转换失败: {e}")
            return []
    
    def show_file_info(self, file_path):
        """显示文件信息"""
        try:
            df = self.read_file(file_path)
            if df is None:
                return
            
            print(f"\n📊 文件信息: {file_path}")
            print("-" * 60)
            print(f"行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            print(f"列名: {', '.join(df.columns.tolist())}")
            
            if len(df) > 0:
                print(f"\n前5行数据预览:")
                print(df.head().to_string())
            
            file_size = os.path.getsize(file_path)
            print(f"\n文件大小: {file_size:,} 字节")
            
        except Exception as e:
            print(f"❌ 显示文件信息失败: {e}")

def show_usage():
    """显示使用说明"""
    print("表格文件格式转换工具")
    print("=" * 50)
    print("支持格式: CSV (.csv), Excel (.xlsx), OpenDocument (.ods)")
    print("\n使用方法:")
    print("1. 转换单个文件:")
    print("   python3 format_converter.py <输入文件> <输出文件>")
    print("   例如: python3 format_converter.py data.csv data.xlsx")
    print("\n2. 转换为所有格式:")
    print("   python3 format_converter.py <输入文件> --all")
    print("   例如: python3 format_converter.py data.csv --all")
    print("\n3. 查看文件信息:")
    print("   python3 format_converter.py <文件> --info")
    print("   例如: python3 format_converter.py data.csv --info")

def main():
    """主函数"""
    try:
        if len(sys.argv) < 2:
            show_usage()
            return
        
        converter = FormatConverter()
        input_file = sys.argv[1]
        
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        if len(sys.argv) == 2:
            # 只有输入文件，显示信息
            converter.show_file_info(input_file)
            
        elif len(sys.argv) == 3:
            option = sys.argv[2]
            
            if option == '--info':
                # 显示文件信息
                converter.show_file_info(input_file)
                
            elif option == '--all':
                # 转换为所有格式
                print(f"正在将 {input_file} 转换为所有支持的格式...")
                output_files = converter.convert_to_all_formats(input_file)
                
                if output_files:
                    print(f"\n✅ 转换完成，生成了 {len(output_files)} 个文件:")
                    for file_path in output_files:
                        print(f"  - {file_path}")
                else:
                    print("\n❌ 转换失败")
                    
            else:
                # 转换为指定格式
                output_file = option
                print(f"正在转换: {input_file} -> {output_file}")
                
                success = converter.convert_file(input_file, output_file)
                if not success:
                    print("❌ 转换失败")
        else:
            show_usage()
    
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()

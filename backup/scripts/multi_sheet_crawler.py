#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多工作表ETF持仓查询工具
按工作表顺序、按表格顺序处理ETF，生成对应的工作表结构
"""

import pandas as pd
import os
import sys
import time
from datetime import datetime
import re

# 添加父目录到路径以导入top10模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from top10 import ETFCrawler

class MultiSheetETFCrawler:
    def __init__(self, input_file_path, sheet_names=None):
        self.input_file_path = input_file_path
        self.file_extension = os.path.splitext(input_file_path)[1].lower()
        self.sheet_names = sheet_names if sheet_names else ["宽基指数ETF", "境外ETF", "债券ETF", "商品和货币ETF"]
        self.all_sheets_data = {}
        self.sheet_etf_mapping = {}
        self.sheet_results = {}
        
    def read_sheets_data(self):
        """读取所有工作表数据"""
        try:
            print(f"正在读取文件: {self.input_file_path}")
            
            if self.file_extension == '.xlsx':
                engine = 'openpyxl'
            elif self.file_extension == '.ods':
                engine = 'odf'
            else:
                print(f"❌ 不支持的文件格式: {self.file_extension}")
                return False
            
            # 读取所有工作表
            self.all_sheets_data = pd.read_excel(self.input_file_path, sheet_name=None, engine=engine)
            sheet_names = list(self.all_sheets_data.keys())
            
            print(f"发现 {len(sheet_names)} 个工作表: {', '.join(sheet_names)}")
            
            # 验证指定的工作表是否存在
            valid_sheets = []
            for sheet_name in self.sheet_names:
                if sheet_name in self.all_sheets_data:
                    valid_sheets.append(sheet_name)
                    print(f"✅ 找到工作表: {sheet_name}")
                else:
                    print(f"❌ 未找到工作表: {sheet_name}")
            
            self.sheet_names = valid_sheets
            return len(valid_sheets) > 0
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return False
    
    def extract_etf_codes_by_sheet(self):
        """按工作表顺序提取ETF代码"""
        total_etf_count = 0
        
        for sheet_name in self.sheet_names:
            df = self.all_sheets_data[sheet_name]
            print(f"\n📋 处理工作表: {sheet_name} ({len(df)}行数据)")
            
            # 按表格顺序（从上到下）提取ETF代码
            etf_codes = []
            for index, row in df.iterrows():
                for column in df.columns:
                    value = row[column]
                    if pd.notna(value):
                        str_value = str(value).strip()
                        # 检查是否是6位数字的ETF代码
                        if re.match(r'^\d{6}$', str_value):
                            if str_value not in etf_codes:
                                etf_codes.append(str_value)
                            break  # 找到ETF代码后跳出列循环，继续下一行
            
            if etf_codes:
                print(f"   提取到 {len(etf_codes)} 个ETF代码（按表格顺序）")
                self.sheet_etf_mapping[sheet_name] = etf_codes
                total_etf_count += len(etf_codes)
            else:
                print(f"   ⚠️  未找到ETF代码")
        
        print(f"\n📊 总计: {total_etf_count} 个ETF代码，分布在 {len(self.sheet_etf_mapping)} 个工作表中")
        return total_etf_count > 0
    
    def process_etfs_by_sheet(self):
        """按工作表顺序、按表格顺序处理ETF"""
        total_processed = 0
        total_success = 0
        total_failed = 0
        
        # 计算总ETF数量
        total_etfs = sum(len(codes) for codes in self.sheet_etf_mapping.values())
        
        print(f"开始按工作表顺序处理 {total_etfs} 个ETF...")
        
        # 按工作表顺序处理
        for sheet_name in self.sheet_names:
            if sheet_name not in self.sheet_etf_mapping:
                continue
                
            etf_codes = self.sheet_etf_mapping[sheet_name]
            print(f"\n🔄 处理工作表: {sheet_name} ({len(etf_codes)}个ETF)")
            
            # 初始化该工作表的结果
            self.sheet_results[sheet_name] = {}
            
            # 按表格顺序处理该工作表的ETF
            for i, etf_code in enumerate(etf_codes, 1):
                total_processed += 1
                print(f"\n{'='*60}")
                print(f"[{sheet_name}] 第{i}/{len(etf_codes)}个: {etf_code} (总进度: {total_processed}/{total_etfs})")
                print(f"{'='*60}")
                
                try:
                    # 创建ETF爬虫实例
                    crawler = ETFCrawler(etf_code)
                    
                    # 获取持仓数据
                    holdings = crawler.get_top_holdings()
                    
                    if holdings:
                        # 格式化持仓数据
                        holdings_str = '、'.join([f"{stock['stock_name']}({stock['holding_ratio']})" 
                                                for stock in holdings])
                        
                        self.sheet_results[sheet_name][etf_code] = {
                            'etf_name': crawler.etf_name,
                            'holdings_str': holdings_str,
                            'status': 'success'
                        }
                        total_success += 1
                        print(f"✅ 成功获取ETF {etf_code} 的持仓数据")
                    else:
                        self.sheet_results[sheet_name][etf_code] = {
                            'etf_name': f'ETF({etf_code})',
                            'holdings_str': '获取失败',
                            'status': 'failed'
                        }
                        total_failed += 1
                        print(f"❌ 无法获取ETF {etf_code} 的持仓数据")
                    
                    # 添加延迟避免被封IP
                    if total_processed < total_etfs:
                        print("等待3秒后继续...")
                        time.sleep(3)
                        
                except Exception as e:
                    print(f"❌ 处理ETF {etf_code} 时出错: {e}")
                    self.sheet_results[sheet_name][etf_code] = {
                        'etf_name': f'ETF({etf_code})',
                        'holdings_str': '获取失败',
                        'status': 'error'
                    }
                    total_failed += 1
        
        print(f"\n{'='*80}")
        print(f"批量获取完成！")
        print(f"成功: {total_success}个, 失败: {total_failed}个, 总计: {total_etfs}个")
        print(f"处理了 {len(self.sheet_results)} 个工作表")
        print(f"{'='*80}")
        
        return total_success > 0
    
    def generate_output_files(self):
        """生成对应工作表结构的输出文件"""
        if not self.sheet_results:
            print("❌ 没有结果数据")
            return False
        
        print("正在生成输出文件...")
        
        # 生成文件名
        base_name = os.path.splitext(os.path.basename(self.input_file_path))[0]
        
        try:
            # 1. 生成Excel文件（保持多工作表结构）
            excel_filename = f"{base_name}_updated.xlsx"
            
            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                for sheet_name, sheet_df in self.all_sheets_data.items():
                    if sheet_name in self.sheet_results:
                        # 更新已处理的工作表
                        print(f"   正在更新工作表: {sheet_name}")
                        merged_df = sheet_df.copy()
                        
                        # 确保有十大持仓列
                        if '十大持仓' not in merged_df.columns:
                            merged_df['十大持仓'] = ''
                        
                        # 按原始顺序更新持仓数据
                        updated_count = 0
                        sheet_results = self.sheet_results[sheet_name]
                        
                        for _, row in merged_df.iterrows():
                            for column in merged_df.columns:
                                value = row[column]
                                if pd.notna(value):
                                    str_value = str(value).strip()
                                    if re.match(r'^\d{6}$', str_value) and str_value in sheet_results:
                                        # 找到对应的ETF代码，更新持仓数据
                                        holdings_str = sheet_results[str_value]['holdings_str']
                                        merged_df.loc[merged_df.index == row.name, '十大持仓'] = holdings_str
                                        updated_count += 1
                                        break
                        
                        print(f"     更新了 {updated_count} 个ETF的持仓数据")
                        merged_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    else:
                        # 保持其他工作表不变
                        print(f"   保持工作表不变: {sheet_name}")
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"✅ Excel文件已保存: {excel_filename}")
            
            # 2. 生成CSV文件（包含工作表信息）
            csv_filename = f"{base_name}_updated.csv"
            all_data = []
            
            for sheet_name in self.sheet_names:
                if sheet_name in self.sheet_results:
                    sheet_results = self.sheet_results[sheet_name]
                    etf_codes = self.sheet_etf_mapping[sheet_name]
                    
                    for etf_code in etf_codes:
                        if etf_code in sheet_results:
                            result = sheet_results[etf_code]
                            all_data.append({
                                '工作表': sheet_name,
                                '代码': etf_code,
                                'ETF名称': result['etf_name'],
                                '十大持仓': result['holdings_str'],
                                '状态': result['status'],
                                '更新时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            })
            
            if all_data:
                csv_df = pd.DataFrame(all_data)
                csv_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"✅ CSV文件已保存: {csv_filename}")
            
            # 3. 生成ODS文件
            ods_filename = f"{base_name}_updated.ods"
            try:
                with pd.ExcelWriter(ods_filename, engine='odf') as writer:
                    for sheet_name, sheet_df in self.all_sheets_data.items():
                        if sheet_name in self.sheet_results:
                            merged_df = sheet_df.copy()
                            if '十大持仓' not in merged_df.columns:
                                merged_df['十大持仓'] = ''
                            
                            sheet_results = self.sheet_results[sheet_name]
                            for _, row in merged_df.iterrows():
                                for column in merged_df.columns:
                                    value = row[column]
                                    if pd.notna(value):
                                        str_value = str(value).strip()
                                        if re.match(r'^\d{6}$', str_value) and str_value in sheet_results:
                                            holdings_str = sheet_results[str_value]['holdings_str']
                                            merged_df.loc[merged_df.index == row.name, '十大持仓'] = holdings_str
                                            break
                            
                            merged_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        else:
                            sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                print(f"✅ ODS文件已保存: {ods_filename}")
            except Exception as e:
                print(f"❌ 保存ODS文件失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成输出文件失败: {e}")
            return False
    
    def run(self):
        """运行完整的处理流程"""
        print("🚀 开始多工作表ETF持仓查询...")
        
        # 1. 读取工作表数据
        if not self.read_sheets_data():
            return False
        
        # 2. 提取ETF代码
        if not self.extract_etf_codes_by_sheet():
            return False
        
        # 3. 处理ETF数据
        if not self.process_etfs_by_sheet():
            return False
        
        # 4. 生成输出文件
        if not self.generate_output_files():
            return False
        
        print("🎉 多工作表ETF持仓查询完成！")
        return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python3 multi_sheet_crawler.py <Excel文件路径> [工作表名称...]")
        print("示例:")
        print("  python3 multi_sheet_crawler.py input/核心ETF十大持仓.xlsx")
        print("  python3 multi_sheet_crawler.py input/核心ETF十大持仓.xlsx 宽基指数ETF 境外ETF")
        return
    
    input_file = sys.argv[1]
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    # 解析工作表名称
    sheet_names = None
    if len(sys.argv) > 2:
        sheet_names = sys.argv[2:]
        print(f"指定工作表: {', '.join(sheet_names)}")
    
    # 创建爬虫实例
    crawler = MultiSheetETFCrawler(input_file, sheet_names)
    
    # 运行处理流程
    success = crawler.run()
    
    if success:
        print("✅ 处理成功完成！")
    else:
        print("❌ 处理失败！")

if __name__ == "__main__":
    main()

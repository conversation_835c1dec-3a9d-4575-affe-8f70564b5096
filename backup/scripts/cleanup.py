#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF项目一键清理脚本
清理生成的文件、临时文件和备份文件
"""

import os
import glob
import shutil
from datetime import datetime, timedelta
import sys

class ProjectCleaner:
    def __init__(self, project_dir="."):
        self.project_dir = os.path.abspath(project_dir)
        
        # 定义清理规则
        self.cleanup_rules = {
            "输出文件": {
                "patterns": [
                    "*_updated.csv",
                    "*_updated.xlsx", 
                    "*_updated.ods",
                    "all_etf_holdings_*.txt",
                    "etf_*_top10_*.txt"
                ],
                "description": "脚本生成的结果文件",
                "default_enabled": True
            },
            "备份文件": {
                "patterns": [
                    "*_backup_*.csv",
                    "*_backup_*.xlsx",
                    "*_backup_*.ods"
                ],
                "description": "自动生成的备份文件",
                "default_enabled": True
            },
            "转换文件": {
                "patterns": [
                    "*_converted.csv",
                    "*_converted.xlsx",
                    "*_converted.ods"
                ],
                "description": "格式转换生成的文件",
                "default_enabled": True
            },
            "临时文件": {
                "patterns": [
                    "__pycache__",
                    "*.pyc",
                    "*.pyo", 
                    "*.tmp",
                    "*~",
                    ".DS_Store"
                ],
                "description": "临时和缓存文件",
                "default_enabled": True
            },
            "测试文件": {
                "patterns": [
                    "test_*.csv",
                    "test_*.xlsx",
                    "test_*.ods",
                    "test_*_updated.*"
                ],
                "description": "测试生成的文件",
                "default_enabled": False
            },
            "报告文件": {
                "patterns": [
                    "file_report_*.txt",
                    "cleanup_report_*.txt"
                ],
                "description": "文件管理报告",
                "default_enabled": False
            }
        }
    
    def scan_files_to_clean(self, categories=None):
        """扫描需要清理的文件"""
        if categories is None:
            categories = list(self.cleanup_rules.keys())
        
        files_to_clean = {}
        
        for category in categories:
            if category not in self.cleanup_rules:
                continue
                
            rule = self.cleanup_rules[category]
            files_to_clean[category] = []
            
            for pattern in rule['patterns']:
                # 使用glob查找匹配的文件
                matches = glob.glob(os.path.join(self.project_dir, pattern), recursive=True)
                
                for match in matches:
                    if os.path.exists(match):
                        file_info = {
                            'path': match,
                            'rel_path': os.path.relpath(match, self.project_dir),
                            'size': self.get_size(match),
                            'mtime': datetime.fromtimestamp(os.path.getmtime(match))
                        }
                        files_to_clean[category].append(file_info)
        
        return files_to_clean
    
    def get_size(self, path):
        """获取文件或目录大小"""
        if os.path.isfile(path):
            return os.path.getsize(path)
        elif os.path.isdir(path):
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, IOError):
                        pass
            return total_size
        return 0
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def generate_cleanup_report(self, categories=None):
        """生成清理报告"""
        files_to_clean = self.scan_files_to_clean(categories)
        
        report = []
        report.append("=" * 80)
        report.append("ETF项目清理报告")
        report.append("=" * 80)
        report.append(f"扫描目录: {self.project_dir}")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        total_files = 0
        total_size = 0
        
        for category, files in files_to_clean.items():
            if not files:
                continue
                
            category_size = sum(f['size'] for f in files)
            total_files += len(files)
            total_size += category_size
            
            rule = self.cleanup_rules[category]
            
            report.append(f"🗂️  {category} ({len(files)}个文件, {self.format_size(category_size)})")
            report.append("-" * 60)
            report.append(f"说明: {rule['description']}")
            report.append(f"默认清理: {'是' if rule['default_enabled'] else '否'}")
            report.append("")
            
            # 按修改时间排序
            sorted_files = sorted(files, key=lambda x: x['mtime'], reverse=True)
            
            for file_info in sorted_files:
                report.append(f"  📄 {file_info['rel_path']}")
                report.append(f"      大小: {self.format_size(file_info['size'])}")
                report.append(f"      修改: {file_info['mtime'].strftime('%Y-%m-%d %H:%M:%S')}")
                report.append("")
        
        report.append("=" * 80)
        report.append(f"清理汇总: {total_files}个文件, 总大小: {self.format_size(total_size)}")
        report.append("=" * 80)
        
        return "\n".join(report), files_to_clean, total_files, total_size
    
    def clean_files(self, categories=None, dry_run=False):
        """清理文件"""
        files_to_clean = self.scan_files_to_clean(categories)
        
        cleaned_files = 0
        cleaned_size = 0
        errors = []
        
        print(f"开始清理文件 ({'预览模式' if dry_run else '实际执行'})...")
        print("=" * 60)
        
        for category, files in files_to_clean.items():
            if not files:
                continue
                
            print(f"\n🗂️  清理 {category} ({len(files)}个文件)")
            
            for file_info in files:
                file_path = file_info['path']
                rel_path = file_info['rel_path']
                
                try:
                    if not dry_run:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                    
                    print(f"  ✅ {'[预览]' if dry_run else '已删除'} {rel_path} ({self.format_size(file_info['size'])})")
                    cleaned_files += 1
                    cleaned_size += file_info['size']
                    
                except Exception as e:
                    error_msg = f"删除失败 {rel_path}: {e}"
                    print(f"  ❌ {error_msg}")
                    errors.append(error_msg)
        
        print("\n" + "=" * 60)
        print(f"清理完成: {cleaned_files}个文件, {self.format_size(cleaned_size)}")
        
        if errors:
            print(f"错误: {len(errors)}个")
            for error in errors:
                print(f"  ❌ {error}")
        
        return cleaned_files, cleaned_size, errors
    
    def clean_old_files(self, days=7, dry_run=False):
        """清理指定天数前的文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # 只清理输出文件和备份文件
        categories = ["输出文件", "备份文件", "转换文件"]
        files_to_clean = self.scan_files_to_clean(categories)
        
        old_files = {}
        for category, files in files_to_clean.items():
            old_files[category] = [f for f in files if f['mtime'] < cutoff_date]
        
        print(f"清理{days}天前的文件 ({'预览模式' if dry_run else '实际执行'})...")
        print(f"截止日期: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        cleaned_files = 0
        cleaned_size = 0
        
        for category, files in old_files.items():
            if not files:
                continue
                
            print(f"\n🗂️  {category} - {len(files)}个旧文件")
            
            for file_info in files:
                try:
                    if not dry_run:
                        if os.path.isfile(file_info['path']):
                            os.remove(file_info['path'])
                        elif os.path.isdir(file_info['path']):
                            shutil.rmtree(file_info['path'])
                    
                    print(f"  ✅ {'[预览]' if dry_run else '已删除'} {file_info['rel_path']} "
                          f"({file_info['mtime'].strftime('%Y-%m-%d')}, {self.format_size(file_info['size'])})")
                    cleaned_files += 1
                    cleaned_size += file_info['size']
                    
                except Exception as e:
                    print(f"  ❌ 删除失败 {file_info['rel_path']}: {e}")
        
        print(f"\n清理完成: {cleaned_files}个文件, {self.format_size(cleaned_size)}")
        return cleaned_files, cleaned_size
    
    def save_report(self, filename=None):
        """保存清理报告"""
        if not filename:
            filename = f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        report, _, _, _ = self.generate_cleanup_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 清理报告已保存: {filename}")
        return filename

def main():
    """主函数"""
    cleaner = ProjectCleaner()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "report":
            # 生成并显示清理报告
            report, _, total_files, total_size = cleaner.generate_cleanup_report()
            print(report)
            
        elif command == "save":
            # 保存清理报告
            cleaner.save_report()
            
        elif command == "preview":
            # 预览清理
            cleaner.clean_files(dry_run=True)
            
        elif command == "clean":
            # 执行清理
            confirm = input("确定要清理文件吗？此操作不可恢复 (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                cleaner.clean_files(dry_run=False)
            else:
                print("❌ 已取消清理")
                
        elif command == "clean-old":
            # 清理旧文件
            days = 7
            if len(sys.argv) > 2:
                try:
                    days = int(sys.argv[2])
                except ValueError:
                    print("❌ 天数必须是数字")
                    return
            
            confirm = input(f"确定要清理{days}天前的文件吗？此操作不可恢复 (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                cleaner.clean_old_files(days=days, dry_run=False)
            else:
                print("❌ 已取消清理")
                
        elif command == "preview-old":
            # 预览清理旧文件
            days = 7
            if len(sys.argv) > 2:
                try:
                    days = int(sys.argv[2])
                except ValueError:
                    print("❌ 天数必须是数字")
                    return
            cleaner.clean_old_files(days=days, dry_run=True)
            
        else:
            print("❌ 未知命令")
            print("可用命令:")
            print("  report      - 显示清理报告")
            print("  save        - 保存清理报告到文件")
            print("  preview     - 预览清理操作")
            print("  clean       - 执行清理")
            print("  clean-old [天数] - 清理指定天数前的文件")
            print("  preview-old [天数] - 预览清理旧文件")
    else:
        # 默认显示清理报告
        report, _, total_files, total_size = cleaner.generate_cleanup_report()
        print(report)
        
        if total_files > 0:
            print("\n💡 提示:")
            print("  python3 cleanup.py preview  - 预览清理操作")
            print("  python3 cleanup.py clean    - 执行清理")

if __name__ == "__main__":
    main()

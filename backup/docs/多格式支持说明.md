# ETF持仓查询工具 - 多格式支持说明

## 概述

本工具现已完全支持CSV、Excel (XLSX)、OpenDocument (ODS)三种主流表格格式，实现了真正的跨平台兼容性。

## 支持的文件格式

| 格式 | 扩展名 | 描述 | 读取引擎 | 写入引擎 |
|------|--------|------|----------|----------|
| CSV | `.csv` | 逗号分隔值文件 | pandas | pandas |
| Excel | `.xlsx` | Microsoft Excel文件 | openpyxl | openpyxl |
| OpenDocument | `.ods` | LibreOffice/OpenOffice表格 | odf | odf |

## 功能特性

### 🔄 智能格式识别
- 自动根据文件扩展名选择合适的读取方法
- 支持多种字符编码（UTF-8、GBK、GB2312等）
- 智能错误处理和格式回退

### 📊 多格式输出
- **一次运行，三种格式**：每次批量查询都会同时生成CSV、XLSX、ODS三种格式的结果文件
- **保持数据一致性**：所有格式包含相同的数据内容
- **优化文件大小**：根据格式特点优化存储

### 🔀 格式转换工具
- 支持任意格式间的相互转换
- 批量转换功能（一键生成所有格式）
- 文件信息查看功能

## 使用示例

### 1. 从不同格式读取ETF列表

#### CSV格式输入
```bash
# 从CSV文件读取ETF列表
python3 batch_etf_crawler.py CoreETFTopTenHoldings.csv
```

#### Excel格式输入
```bash
# 从Excel文件读取ETF列表
python3 batch_etf_crawler.py CoreETFTopTenHoldings.xlsx
```

#### ODS格式输入
```bash
# 从ODS文件读取ETF列表
python3 batch_etf_crawler.py CoreETFTopTenHoldings.ods
```

### 2. 自动格式检测
```bash
# 自动查找并使用第一个找到的文件
python3 batch_etf_crawler.py
```
查找顺序：`.ods` → `.xlsx` → `.csv`

### 3. 格式转换
```bash
# 查看文件信息
python3 format_converter.py data.csv --info

# 转换为Excel格式
python3 format_converter.py data.csv data.xlsx

# 转换为所有格式
python3 format_converter.py data.csv --all
```

## 输出文件说明

### 批量查询输出
每次运行批量查询都会生成以下文件：

```
输入文件: CoreETFTopTenHoldings.csv
输出文件:
├── CoreETFTopTenHoldings_updated.csv    # CSV格式结果
├── CoreETFTopTenHoldings_updated.xlsx   # Excel格式结果
├── CoreETFTopTenHoldings_updated.ods    # ODS格式结果
└── all_etf_holdings_20250915_180305.txt # 文本汇总报告
```

### 文件大小对比
基于20个ETF的实际测试数据：

| 格式 | 文件大小 | 特点 |
|------|----------|------|
| CSV | 5,516 字节 | 最小，纯文本，兼容性最好 |
| XLSX | 7,798 字节 | 中等，支持格式化，Excel原生 |
| ODS | 4,461 字节 | 较小，开源标准，LibreOffice原生 |

## 技术实现

### 读取实现
```python
# CSV读取 - 支持多种编码
for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
    try:
        df = pd.read_csv(file_path, encoding=encoding)
        break
    except UnicodeDecodeError:
        continue

# Excel读取
df = pd.read_excel(file_path, engine='openpyxl')

# ODS读取
df = pd.read_excel(file_path, engine='odf')
```

### 写入实现
```python
# CSV写入 - 使用UTF-8 BOM确保兼容性
df.to_csv(filename, index=False, encoding='utf-8-sig')

# Excel写入
df.to_excel(filename, index=False, engine='openpyxl')

# ODS写入
df.to_excel(filename, index=False, engine='odf')
```

## 兼容性说明

### 软件兼容性
| 格式 | Microsoft Excel | LibreOffice Calc | Google Sheets | WPS Office |
|------|-----------------|------------------|---------------|------------|
| CSV | ✅ 完全支持 | ✅ 完全支持 | ✅ 完全支持 | ✅ 完全支持 |
| XLSX | ✅ 原生支持 | ✅ 完全支持 | ✅ 完全支持 | ✅ 完全支持 |
| ODS | ⚠️ 基本支持 | ✅ 原生支持 | ⚠️ 基本支持 | ⚠️ 基本支持 |

### 平台兼容性
- **Windows**: 所有格式完全支持
- **macOS**: 所有格式完全支持  
- **Linux**: 所有格式完全支持

## 最佳实践建议

### 1. 格式选择建议
- **CSV**: 适合数据交换、脚本处理、版本控制
- **XLSX**: 适合Excel用户、需要格式化的场景
- **ODS**: 适合开源环境、LibreOffice用户

### 2. 工作流建议
1. **数据准备**: 使用任意格式准备ETF列表
2. **批量查询**: 运行脚本获取三种格式的结果
3. **数据使用**: 根据需要选择合适格式的结果文件
4. **格式转换**: 需要时使用转换工具进行格式转换

### 3. 文件管理建议
- 保持输入文件的原始格式
- 定期清理临时和备份文件
- 使用有意义的文件名区分不同批次的数据

## 错误处理

### 常见问题及解决方案

1. **编码问题**
   - 问题：CSV文件中文显示乱码
   - 解决：脚本自动尝试多种编码格式

2. **格式不支持**
   - 问题：文件格式不被识别
   - 解决：检查文件扩展名，确保为.csv/.xlsx/.ods

3. **依赖缺失**
   - 问题：缺少openpyxl或odfpy库
   - 解决：运行 `pip install -r requirements.txt`

## 更新日志

### v2.0 - 多格式支持版本
- ✅ 新增CSV格式支持
- ✅ 新增Excel (XLSX)格式支持  
- ✅ 保持ODS格式支持
- ✅ 新增格式转换工具
- ✅ 智能格式识别
- ✅ 同时输出三种格式
- ✅ 完善错误处理

### v1.0 - 基础版本
- ✅ ODS格式支持
- ✅ 批量ETF数据获取
- ✅ 文本报告生成

## 总结

通过多格式支持的完善，本工具现在能够：
- 📖 **读取**：CSV、XLSX、ODS三种格式的输入文件
- 📝 **输出**：同时生成三种格式的结果文件
- 🔄 **转换**：支持格式间的相互转换
- 🌐 **兼容**：跨平台、跨软件的广泛兼容性

这使得工具能够适应不同用户的使用习惯和技术环境，真正实现了"一次开发，处处可用"的目标。
